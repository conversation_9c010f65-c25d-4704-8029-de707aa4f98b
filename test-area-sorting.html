<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地区排序测试</title>
</head>
<body>
    <h1>地区排序测试</h1>
    <div id="result"></div>

    <script src="src/static/area.js"></script>
    <script>
        // 测试省级排序
        console.log('省级排序测试:');
        const provinces = district[0];
        provinces.forEach((item, index) => {
            console.log(`${index + 1}. ${item.label} (value: ${item.value})`);
        });

        // 测试北京区县排序
        console.log('\n北京区县排序测试:');
        const beijingDistricts = district['0101'];
        if (beijingDistricts) {
            beijingDistricts.forEach((item, index) => {
                console.log(`${index + 1}. ${item.label} (value: ${item.value})`);
            });
        }

        // 测试上海区县排序
        console.log('\n上海区县排序测试:');
        const shanghaiDistricts = district['0201'];
        if (shanghaiDistricts) {
            shanghaiDistricts.forEach((item, index) => {
                console.log(`${index + 1}. ${item.label} (value: ${item.value})`);
            });
        }

        // 测试广东省城市排序
        console.log('\n广东省城市排序测试:');
        const guangdongCities = district['03'];
        if (guangdongCities) {
            guangdongCities.forEach((item, index) => {
                console.log(`${index + 1}. ${item.label} (value: ${item.value})`);
            });
        }

        // 在页面上显示结果
        const resultDiv = document.getElementById('result');
        resultDiv.innerHTML = `
            <h2>省级排序 (前10位):</h2>
            <ol>
                ${provinces.slice(0, 10).map(item => `<li>${item.label} (${item.value})</li>`).join('')}
            </ol>
            
            <h2>北京区县排序:</h2>
            <ol>
                ${beijingDistricts ? beijingDistricts.map(item => `<li>${item.label} (${item.value})</li>`).join('') : '数据未找到'}
            </ol>
            
            <h2>上海区县排序:</h2>
            <ol>
                ${shanghaiDistricts ? shanghaiDistricts.map(item => `<li>${item.label} (${item.value})</li>`).join('') : '数据未找到'}
            </ol>
            
            <h2>广东省城市排序 (前10位):</h2>
            <ol>
                ${guangdongCities ? guangdongCities.slice(0, 10).map(item => `<li>${item.label} (${item.value})</li>`).join('') : '数据未找到'}
            </ol>
        `;
    </script>
</body>
</html>
